@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom components */
@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 w-full;
  }

  .btn-secondary {
    @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 placeholder-gray-500;
  }

  .form-group {
    @apply mb-6;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }

  .error-message {
    @apply text-red-600 text-sm mt-1;
  }

  .success-message {
    @apply text-green-600 text-sm mt-1;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }

  .loading {
    @apply opacity-50 pointer-events-none;
  }

  /* Auth Layout Styles */
  .auth-container {
    @apply min-h-screen flex;
  }

  .auth-left {
    @apply bg-gradient-to-br from-blue-900 via-blue-800 to-purple-900 relative overflow-hidden;
    flex: 0 0 65%;
  }

  .auth-right {
    @apply bg-white flex items-center justify-center p-6;
    flex: 0 0 35%;
  }

  .auth-brand {
    @apply text-white text-center relative z-10;
  }

  .auth-brand-icon {
    @apply w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mx-auto mb-8 backdrop-blur-sm;
  }

  .auth-brand-title {
    @apply text-4xl font-bold mb-4;
  }

  .auth-brand-subtitle {
    @apply text-blue-200 text-lg mb-12;
  }

  .auth-features {
    @apply grid grid-cols-3 gap-6 max-w-4xl mx-auto;
  }

  .auth-feature {
    @apply text-center text-white;
  }

  .auth-feature-icon {
    @apply w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mx-auto mb-4 backdrop-blur-sm;
  }

  .auth-feature-title {
    @apply font-semibold mb-2;
  }

  .auth-feature-desc {
    @apply text-blue-200 text-sm;
  }

  .auth-form-container {
    @apply w-full max-w-md;
  }

  .auth-form-header {
    @apply text-center mb-8;
  }

  .auth-form-title {
    @apply text-2xl font-bold text-gray-800 mb-2;
  }

  .auth-form-subtitle {
    @apply text-gray-600;
  }

  .auth-tabs {
    @apply flex mb-8 bg-gray-100 rounded-lg p-1;
  }

  .auth-tab {
    @apply flex-1 py-2 px-4 text-center rounded-md transition-colors duration-200 cursor-pointer;
  }

  .auth-tab.active {
    @apply bg-white text-blue-600 font-medium shadow-sm;
  }

  .auth-tab:not(.active) {
    @apply text-gray-600 hover:text-gray-800;
  }

  .auth-background-pattern {
    @apply absolute inset-0 opacity-10;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(255,255,255,0.2) 2px, transparent 2px),
      radial-gradient(circle at 75% 75%, rgba(255,255,255,0.2) 2px, transparent 2px);
    background-size: 50px 50px;
  }

  .auth-background-glow {
    @apply absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full opacity-20 blur-3xl;
  }

  .checkbox-container {
    @apply flex items-center space-x-2 mb-6;
  }

  .checkbox-input {
    @apply w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500;
  }

  .checkbox-label {
    @apply text-sm text-gray-600;
  }

  .link-text {
    @apply text-blue-600 hover:text-blue-800 underline;
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .auth-left {
      flex: 0 0 60%;
    }

    .auth-right {
      flex: 0 0 40%;
      @apply p-4;
    }

    .auth-features {
      @apply grid-cols-1 gap-4 max-w-md;
    }
  }

  @media (max-width: 768px) {
    .auth-container {
      @apply flex-col;
    }

    .auth-left {
      @apply min-h-[40vh] flex-none;
      flex: none;
    }

    .auth-right {
      @apply flex-1 p-4;
      flex: 1;
    }

    .auth-features {
      @apply grid-cols-3 gap-4;
    }

    .auth-feature-desc {
      @apply hidden;
    }

    .auth-brand-title {
      @apply text-3xl;
    }

    .auth-brand-subtitle {
      @apply text-base mb-8;
    }
  }

  @media (max-width: 480px) {
    .auth-left {
      @apply min-h-[30vh] p-6;
    }

    .auth-features {
      @apply hidden;
    }

    .auth-brand-title {
      @apply text-2xl;
    }

    .auth-form-container {
      @apply max-w-full;
    }
  }
}