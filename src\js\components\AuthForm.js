import { DOMUtils } from '../utils/dom.js';
import { Validator, VALIDATION_RULES } from '../utils/validation.js';
import authService from '../services/authService.js';

export class AuthForm {
  constructor(containerId) {
    this.container = DOMUtils.getElementById(containerId);
    this.currentForm = 'login'; // 'login', 'register', 'profile'
    this.init();
  }

  init() {
    this.render();
    this.attachEventListeners();
  }

  render() {
    const content = this.currentForm === 'profile' ? this.renderProfileForm() : this.renderAuthForm();
    DOMUtils.setContent(this.container, content);
  }

  // Render combined auth form (login + register)
  renderAuthForm() {
    return `
      <div class="auth-container">
        <!-- Left Side - Branding (70% width) -->
        <div class="auth-left">
          <div class="auth-background-pattern"></div>
          <div class="auth-background-glow"></div>

          <div class="flex flex-col justify-center h-full p-12 relative z-10">
            <div class="auth-brand">
              <div class="auth-brand-icon">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
              </div>
              <h1 class="auth-brand-title">Peta Talenta</h1>
              <p class="auth-brand-subtitle">Platform Pemetaan dan Analisis Talenta Berbasis AI</p>
            </div>

            <div class="auth-features">
              <div class="auth-feature">
                <div class="auth-feature-icon">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <h3 class="auth-feature-title">Asesmen Cerdas</h3>
                <p class="auth-feature-desc">Algoritma AI canggih untuk evaluasi talenta yang akurat</p>
              </div>

              <div class="auth-feature">
                <div class="auth-feature-icon">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  </svg>
                </div>
                <h3 class="auth-feature-title">Wawasan Bertenaga AI</h3>
                <p class="auth-feature-desc">Rekomendasi berbasis data untuk keputusan yang lebih baik</p>
              </div>

              <div class="auth-feature">
                <div class="auth-feature-icon">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <h3 class="auth-feature-title">Analitik Real-time</h3>
                <p class="auth-feature-desc">Pantau kemajuan dan kinerja secara real-time</p>
              </div>
            </div>

            <div class="text-center mt-12">
              <p class="text-blue-200 text-sm">"Temukan potensi terbaik Anda dengan Peta Talenta"</p>
            </div>
          </div>
        </div>

        <!-- Right Side - Auth Form (30% width) -->
        <div class="auth-right">
          <div class="auth-form-container">
            <div class="auth-form-header">
              <div class="auth-tabs">
                <div class="auth-tab ${this.currentForm === 'login' ? 'active' : ''}" id="loginTab">Masuk</div>
                <div class="auth-tab ${this.currentForm === 'register' ? 'active' : ''}" id="registerTab">Daftar</div>
              </div>
              <h2 class="auth-form-title">${this.currentForm === 'login' ? 'Selamat Datang Kembali' : 'Buat Akun Anda'}</h2>
              <p class="auth-form-subtitle">${this.currentForm === 'login' ? 'Masuk ke akun Anda untuk melanjutkan' : 'Bergabunglah dengan Peta Talenta hari ini'}</p>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="space-y-6 ${this.currentForm === 'login' ? '' : 'hidden'}">
              <div class="form-group">
                <label for="loginEmail" class="form-label">Alamat Email</label>
                <input type="email" id="loginEmail" name="email" class="input-field" placeholder="Masukkan email Anda" required>
                <div id="loginEmailError" class="error-message hidden"></div>
              </div>

              <div class="form-group">
                <label for="loginPassword" class="form-label">Kata Sandi</label>
                <input type="password" id="loginPassword" name="password" class="input-field" placeholder="Masukkan kata sandi Anda" required>
                <div id="loginPasswordError" class="error-message hidden"></div>
              </div>

              <div id="loginError" class="error-message hidden"></div>
              <div id="loginSuccess" class="success-message hidden"></div>

              <button type="submit" id="loginBtn" class="btn-primary">
                <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
                Masuk
              </button>
            </form>

            <!-- Register Form -->
            <form id="registerForm" class="space-y-6 ${this.currentForm === 'register' ? '' : 'hidden'}">
              <div class="form-group">
                <label for="registerEmail" class="form-label">Alamat Email</label>
                <input type="email" id="registerEmail" name="email" class="input-field" placeholder="Masukkan email Anda" required>
                <div id="registerEmailError" class="error-message hidden"></div>
              </div>

              <div class="form-group">
                <label for="registerPassword" class="form-label">Kata Sandi</label>
                <input type="password" id="registerPassword" name="password" class="input-field" placeholder="Masukkan kata sandi Anda" required>
                <div id="registerPasswordError" class="error-message hidden"></div>
              </div>

              <div class="form-group">
                <label for="confirmPassword" class="form-label">Konfirmasi Kata Sandi</label>
                <input type="password" id="confirmPassword" name="confirmPassword" class="input-field" placeholder="Konfirmasi kata sandi Anda" required>
                <div id="confirmPasswordError" class="error-message hidden"></div>
              </div>

              <div class="checkbox-container">
                <input type="checkbox" id="agreeTerms" class="checkbox-input" required>
                <label for="agreeTerms" class="checkbox-label">
                  Saya setuju dengan <a href="#" class="link-text">Syarat dan Ketentuan</a> serta <a href="#" class="link-text">Kebijakan Privasi</a>
                </label>
              </div>

              <div id="registerError" class="error-message hidden"></div>
              <div id="registerSuccess" class="success-message hidden"></div>

              <button type="submit" id="registerBtn" class="btn-primary">
                <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                </svg>
                Buat Akun
              </button>
            </form>

            <div class="mt-6 text-center">
              <p class="text-sm text-gray-500">© 2024 Peta Talenta. Semua hak dilindungi.</p>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  renderProfileForm() {
    const user = authService.getCurrentUser();
    return `
      <div class="min-h-screen bg-gray-100">
        <div class="max-w-6xl mx-auto p-8">
          <div class="bg-white rounded-lg shadow-md">
            <div class="flex justify-between items-center p-6 border-b">
              <div>
                <h2 class="text-2xl font-bold text-gray-800">Profile Dashboard</h2>
                <p class="text-gray-600">Manage your account and preferences</p>
              </div>
              <button id="logoutBtn" class="btn-secondary">Logout</button>
            </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 class="text-lg font-semibold mb-4">User Information</h3>
              <div class="space-y-2 text-sm">
                <p><strong>Email:</strong> ${user?.email || 'N/A'}</p>
                <p><strong>User Type:</strong> ${user?.user_type || 'N/A'}</p>
                <p><strong>Token Balance:</strong> ${user?.token_balance || 0}</p>
                <p><strong>Status:</strong> ${user?.is_active ? 'Active' : 'Inactive'}</p>
              </div>
            </div>
            
            <div>
              <h3 class="text-lg font-semibold mb-4">Update Profile</h3>
              <form id="profileForm" class="space-y-4">
                <div class="form-group">
                  <label for="username" class="form-label">Username</label>
                  <input type="text" id="username" name="username" class="input-field">
                  <div id="usernameError" class="error-message hidden"></div>
                </div>
                
                <div class="form-group">
                  <label for="fullName" class="form-label">Full Name</label>
                  <input type="text" id="fullName" name="full_name" class="input-field">
                  <div id="fullNameError" class="error-message hidden"></div>
                </div>
                
                <div class="form-group">
                  <label for="dateOfBirth" class="form-label">Date of Birth</label>
                  <input type="date" id="dateOfBirth" name="date_of_birth" class="input-field">
                  <div id="dateOfBirthError" class="error-message hidden"></div>
                </div>
                
                <div class="form-group">
                  <label for="gender" class="form-label">Gender</label>
                  <select id="gender" name="gender" class="input-field">
                    <option value="">Select Gender</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                  </select>
                  <div id="genderError" class="error-message hidden"></div>
                </div>
                
                <div class="form-group">
                  <label for="schoolId" class="form-label">School ID</label>
                  <input type="number" id="schoolId" name="school_id" class="input-field">
                  <div id="schoolIdError" class="error-message hidden"></div>
                </div>
                
                <div id="profileError" class="error-message hidden"></div>
                <div id="profileSuccess" class="success-message hidden"></div>
                
                <button type="submit" id="updateProfileBtn" class="btn-primary w-full">
                  Update Profile
                </button>
              </form>
            </div>
          </div>
          
          <div class="mt-8 pt-6 border-t">
            <h3 class="text-lg font-semibold mb-4">Change Password</h3>
            <form id="changePasswordForm" class="max-w-md space-y-4">
              <div class="form-group">
                <label for="currentPassword" class="form-label">Current Password</label>
                <input type="password" id="currentPassword" name="currentPassword" class="input-field" required>
                <div id="currentPasswordError" class="error-message hidden"></div>
              </div>
              
              <div class="form-group">
                <label for="newPassword" class="form-label">New Password</label>
                <input type="password" id="newPassword" name="newPassword" class="input-field" required>
                <div id="newPasswordError" class="error-message hidden"></div>
              </div>
              
              <div id="changePasswordError" class="error-message hidden"></div>
              <div id="changePasswordSuccess" class="success-message hidden"></div>
              
              <button type="submit" id="changePasswordBtn" class="btn-primary">
                Change Password
              </button>
            </form>
          </div>
        </div>
      </div>
    `;
  }

  attachEventListeners() {
    // Remove existing listeners
    this.container.removeEventListener('click', this.handleClick);
    this.container.removeEventListener('submit', this.handleSubmit);
    
    // Add new listeners
    this.container.addEventListener('click', this.handleClick.bind(this));
    this.container.addEventListener('submit', this.handleSubmit.bind(this));
  }

  handleClick(event) {
    const { target } = event;

    if (target.id === 'showRegisterBtn' || target.id === 'registerTab') {
      this.switchToTab('register');
    } else if (target.id === 'showLoginBtn' || target.id === 'loginTab') {
      this.switchToTab('login');
    } else if (target.id === 'logoutBtn') {
      this.handleLogout();
    }
  }

  // Switch between login and register tabs without full re-render
  switchToTab(formType) {
    this.currentForm = formType;

    // Update tab active states
    const loginTab = DOMUtils.getElementById('loginTab');
    const registerTab = DOMUtils.getElementById('registerTab');
    const loginForm = DOMUtils.getElementById('loginForm');
    const registerForm = DOMUtils.getElementById('registerForm');
    const formTitle = document.querySelector('.auth-form-title');
    const formSubtitle = document.querySelector('.auth-form-subtitle');

    if (loginTab && registerTab && loginForm && registerForm && formTitle && formSubtitle) {
      // Update tab states
      if (formType === 'login') {
        loginTab.classList.add('active');
        registerTab.classList.remove('active');
        loginForm.classList.remove('hidden');
        registerForm.classList.add('hidden');
        formTitle.textContent = 'Selamat Datang Kembali';
        formSubtitle.textContent = 'Masuk ke akun Anda untuk melanjutkan';
      } else {
        loginTab.classList.remove('active');
        registerTab.classList.add('active');
        loginForm.classList.add('hidden');
        registerForm.classList.remove('hidden');
        formTitle.textContent = 'Buat Akun Anda';
        formSubtitle.textContent = 'Bergabunglah dengan Peta Talenta hari ini';
      }

      // Clear any error messages
      this.clearAllErrors();
    }
  }

  // Clear all error and success messages
  clearAllErrors() {
    const errorElements = this.container.querySelectorAll('.error-message, .success-message');
    errorElements.forEach(el => {
      el.classList.add('hidden');
      el.textContent = '';
    });
  }

  async handleSubmit(event) {
    event.preventDefault();
    const form = event.target;
    
    if (form.id === 'loginForm') {
      await this.handleLogin(form);
    } else if (form.id === 'registerForm') {
      await this.handleRegister(form);
    } else if (form.id === 'profileForm') {
      await this.handleUpdateProfile(form);
    } else if (form.id === 'changePasswordForm') {
      await this.handleChangePassword(form);
    }
  }

  async handleLogin(form) {
    const formData = DOMUtils.getFormData(form);
    const submitBtn = DOMUtils.getElementById('loginBtn');
    const errorEl = DOMUtils.getElementById('loginError');
    const successEl = DOMUtils.getElementById('loginSuccess');
    
    // Clear previous messages
    DOMUtils.hideError(errorEl);
    DOMUtils.hideSuccess(successEl);
    
    // Validate form
    const validation = Validator.validateForm(formData, {
      email: VALIDATION_RULES.email,
      password: VALIDATION_RULES.password
    });
    
    if (!validation.isValid) {
      this.showFieldErrors('login', validation.errors);
      return;
    }
    
    try {
      DOMUtils.setLoading(submitBtn, true);
      
      const result = await authService.login(formData.email, formData.password);
      
      if (result.success) {
        DOMUtils.showSuccess(successEl, 'Login berhasil!');
        setTimeout(() => {
          this.currentForm = 'profile';
          this.render();
          this.attachEventListeners();
        }, 1000);
      }
    } catch (error) {
      DOMUtils.showError(errorEl, error.message);
    } finally {
      DOMUtils.setLoading(submitBtn, false);
    }
  }

  async handleRegister(form) {
    const formData = DOMUtils.getFormData(form);
    const submitBtn = DOMUtils.getElementById('registerBtn');
    const errorEl = DOMUtils.getElementById('registerError');
    const successEl = DOMUtils.getElementById('registerSuccess');

    // Clear previous messages
    DOMUtils.hideError(errorEl);
    DOMUtils.hideSuccess(successEl);

    // Check if passwords match
    if (formData.password !== formData.confirmPassword) {
      const confirmPasswordError = DOMUtils.getElementById('confirmPasswordError');
      DOMUtils.showError(confirmPasswordError, 'Kata sandi tidak cocok');
      return;
    }

    // Check terms agreement
    if (!formData.agreeTerms) {
      DOMUtils.showError(errorEl, 'Anda harus menyetujui Syarat dan Ketentuan');
      return;
    }

    // Validate form
    const validation = Validator.validateForm(formData, {
      email: VALIDATION_RULES.email,
      password: VALIDATION_RULES.password
    });

    if (!validation.isValid) {
      this.showFieldErrors('register', validation.errors);
      return;
    }

    try {
      DOMUtils.setLoading(submitBtn, true);

      const result = await authService.register(formData.email, formData.password);

      if (result.success) {
        DOMUtils.showSuccess(successEl, 'Pendaftaran berhasil!');
        setTimeout(() => {
          this.currentForm = 'profile';
          this.render();
          this.attachEventListeners();
        }, 1000);
      }
    } catch (error) {
      DOMUtils.showError(errorEl, error.message);
    } finally {
      DOMUtils.setLoading(submitBtn, false);
    }
  }

  async handleUpdateProfile(form) {
    const formData = DOMUtils.getFormData(form);
    const submitBtn = DOMUtils.getElementById('updateProfileBtn');
    const errorEl = DOMUtils.getElementById('profileError');
    const successEl = DOMUtils.getElementById('profileSuccess');
    
    // Clear previous messages
    DOMUtils.hideError(errorEl);
    DOMUtils.hideSuccess(successEl);
    
    // Filter out empty values
    const profileData = {};
    Object.entries(formData).forEach(([key, value]) => {
      if (value && value.trim() !== '') {
        profileData[key] = key === 'school_id' ? parseInt(value) : value;
      }
    });
    
    // Validate form
    const validation = Validator.validateForm(profileData, {
      username: VALIDATION_RULES.username,
      full_name: VALIDATION_RULES.fullName,
      date_of_birth: VALIDATION_RULES.dateOfBirth,
      gender: VALIDATION_RULES.gender,
      school_id: VALIDATION_RULES.schoolId
    });
    
    if (!validation.isValid) {
      this.showFieldErrors('profile', validation.errors);
      return;
    }
    
    try {
      DOMUtils.setLoading(submitBtn, true);
      
      const result = await authService.updateProfile(profileData);
      
      if (result.success) {
        DOMUtils.showSuccess(successEl, 'Profile updated successfully!');
        // Refresh the form to show updated data
        setTimeout(() => {
          this.render();
          this.attachEventListeners();
        }, 1000);
      }
    } catch (error) {
      DOMUtils.showError(errorEl, error.message);
    } finally {
      DOMUtils.setLoading(submitBtn, false);
    }
  }

  async handleChangePassword(form) {
    const formData = DOMUtils.getFormData(form);
    const submitBtn = DOMUtils.getElementById('changePasswordBtn');
    const errorEl = DOMUtils.getElementById('changePasswordError');
    const successEl = DOMUtils.getElementById('changePasswordSuccess');
    
    // Clear previous messages
    DOMUtils.hideError(errorEl);
    DOMUtils.hideSuccess(successEl);
    
    // Validate form
    const validation = Validator.validateForm(formData, {
      currentPassword: [{ required: true, message: 'Kata sandi saat ini wajib diisi' }],
      newPassword: VALIDATION_RULES.password
    });
    
    if (!validation.isValid) {
      this.showFieldErrors('changePassword', validation.errors);
      return;
    }
    
    try {
      DOMUtils.setLoading(submitBtn, true);
      
      const result = await authService.changePassword(formData.currentPassword, formData.newPassword);
      
      if (result.success) {
        DOMUtils.showSuccess(successEl, 'Kata sandi berhasil diubah!');
        DOMUtils.clearForm(form);
      }
    } catch (error) {
      DOMUtils.showError(errorEl, error.message);
    } finally {
      DOMUtils.setLoading(submitBtn, false);
    }
  }

  async handleLogout() {
    try {
      await authService.logout();
      this.currentForm = 'login';
      this.render();
      this.attachEventListeners();
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if API call fails
      authService.logout();
      this.currentForm = 'login';
      this.render();
      this.attachEventListeners();
    }
  }

  showFieldErrors(formType, errors) {
    Object.entries(errors).forEach(([field, message]) => {
      const errorEl = DOMUtils.getElementById(`${formType}${field.charAt(0).toUpperCase() + field.slice(1)}Error`) ||
                     DOMUtils.getElementById(`${field}Error`);
      if (errorEl) {
        DOMUtils.showError(errorEl, message);
      }
    });
  }

  // Public method to switch forms
  switchToForm(formType) {
    this.currentForm = formType;
    this.render();
    this.attachEventListeners();
  }

  // Public method to check auth state and show appropriate form
  checkAuthState() {
    if (authService.isAuthenticated()) {
      this.currentForm = 'profile';
    } else {
      this.currentForm = 'login';
    }
    this.render();
    this.attachEventListeners();
  }
}
